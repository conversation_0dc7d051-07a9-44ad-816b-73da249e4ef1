# NomaToken Production Environment Configuration
# Copy this file to .env.production and update with your actual values

# Environment
NODE_ENV=production

# Reown AppKit Configuration
NEXT_PUBLIC_REOWN_PROJECT_ID=1eeba23b8a565e4340dab5918b836327

# BSC Network Configuration
NEXT_PUBLIC_BSC_RPC_URL=https://bsc-dataseed1.binance.org/
NEXT_PUBLIC_BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# NOMA Token Contract (Update with actual deployed contract addresses)
NEXT_PUBLIC_NOMA_TOKEN_CONTRACT=0x0000000000000000000000000000000000000000
NEXT_PUBLIC_TOKEN_SALE_CONTRACT=0x0000000000000000000000000000000000000000

# Supported Tokens for Purchase (BSC Mainnet)
NEXT_PUBLIC_USDT_CONTRACT=0x55d398326f99059fF775485246999027B3197955
NEXT_PUBLIC_BUSD_CONTRACT=0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56

# Network Configuration
NEXT_PUBLIC_DEFAULT_CHAIN_ID=56
NEXT_PUBLIC_ENABLE_TESTNET=false

# M-Pesa Production Configuration
# ⚠️ IMPORTANT: Replace these with your actual Safaricom production credentials
MPESA_ENVIRONMENT=production
MPESA_CONSUMER_KEY=YOUR_PRODUCTION_CONSUMER_KEY_HERE
MPESA_CONSUMER_SECRET=YOUR_PRODUCTION_CONSUMER_SECRET_HERE
MPESA_BUSINESS_SHORT_CODE=YOUR_PRODUCTION_SHORT_CODE_HERE
MPESA_PASSKEY=YOUR_PRODUCTION_PASSKEY_HERE
MPESA_CALLBACK_URL=https://nomatoken.com/api/mpesa/payment/callback

# M-Pesa Security & Rate Limiting (Production Settings)
MPESA_MAX_REQUESTS_PER_MINUTE=30
MPESA_MAX_PAYMENT_ATTEMPTS=5
MPESA_PAYMENT_TIMEOUT_MS=60000
MPESA_STATUS_CHECK_INTERVAL_MS=5000

# Token Configuration
NOMA_TOKEN_PRICE=0.0245
MIN_PURCHASE_AMOUNT=1
MAX_PURCHASE_AMOUNT=1000

# API Configuration
NEXT_PUBLIC_API_URL=https://nomatoken.com/api

# Server Configuration for cPanel
PORT=3000
HOSTNAME=0.0.0.0

# Security Configuration
ALLOWED_ORIGINS=https://nomatoken.com,https://www.nomatoken.com

# Logging Configuration
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Performance Configuration
ENABLE_COMPRESSION=true
CACHE_CONTROL_MAX_AGE=3600
