#!/usr/bin/env node

/**
 * cPanel Node.js Deployment Diagnostic Script
 * 
 * This script helps diagnose common issues with cPanel Node.js deployments
 * Run this script in your cPanel terminal or via SSH to identify problems
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 NomaToken cPanel Deployment Diagnostic Tool\n');

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`, exists ? 'green' : 'red');
  return exists;
}

function checkDirectory(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`, exists ? 'green' : 'red');
  return exists;
}

function runCommand(command, description) {
  try {
    const output = execSync(command, { encoding: 'utf8', timeout: 10000 });
    log(`✅ ${description}`, 'green');
    return output.trim();
  } catch (error) {
    log(`❌ ${description}: ${error.message}`, 'red');
    return null;
  }
}

// 1. Check Node.js Environment
log('\n📋 1. CHECKING NODE.JS ENVIRONMENT', 'blue');
const nodeVersion = runCommand('node --version', 'Node.js version check');
const npmVersion = runCommand('npm --version', 'npm version check');

if (nodeVersion) {
  const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
  if (majorVersion >= 18) {
    log(`✅ Node.js version ${nodeVersion} is compatible`, 'green');
  } else {
    log(`⚠️ Node.js version ${nodeVersion} may be too old (recommend 18+)`, 'yellow');
  }
}

// 2. Check File Structure
log('\n📁 2. CHECKING FILE STRUCTURE', 'blue');
const requiredFiles = [
  { path: 'server.js', desc: 'Main server file' },
  { path: 'package.json', desc: 'Package configuration' },
  { path: '.env', desc: 'Environment variables' }
];

const requiredDirs = [
  { path: '.next', desc: 'Next.js build directory' },
  { path: 'public', desc: 'Static assets directory' }
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (!checkFile(file.path, file.desc)) {
    allFilesExist = false;
  }
});

requiredDirs.forEach(dir => {
  if (!checkDirectory(dir.path, dir.desc)) {
    allFilesExist = false;
  }
});

// 3. Check Package.json
log('\n📦 3. CHECKING PACKAGE.JSON', 'blue');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  log(`✅ Package name: ${packageJson.name}`, 'green');
  log(`✅ Package version: ${packageJson.version}`, 'green');
  
  if (packageJson.scripts && packageJson.scripts.start) {
    log(`✅ Start script: ${packageJson.scripts.start}`, 'green');
  } else {
    log(`❌ No start script found in package.json`, 'red');
  }

  // Check for Next.js dependency
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  if (deps.next) {
    log(`✅ Next.js dependency: ${deps.next}`, 'green');
  } else {
    log(`❌ Next.js dependency not found`, 'red');
  }
} catch (error) {
  log(`❌ Error reading package.json: ${error.message}`, 'red');
}

// 4. Check Environment Variables
log('\n🔧 4. CHECKING ENVIRONMENT VARIABLES', 'blue');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  
  log(`✅ Environment file contains ${envLines.length} variables`, 'green');
  
  // Check for critical variables
  const criticalVars = ['NODE_ENV', 'PORT'];
  criticalVars.forEach(varName => {
    const hasVar = envLines.some(line => line.startsWith(`${varName}=`));
    log(`${hasVar ? '✅' : '⚠️'} ${varName}: ${hasVar ? 'Set' : 'Not set'}`, hasVar ? 'green' : 'yellow');
  });
} catch (error) {
  log(`❌ Error reading .env file: ${error.message}`, 'red');
}

// 5. Check .next Build Directory
log('\n🏗️ 5. CHECKING NEXT.JS BUILD', 'blue');
const nextDirs = [
  { path: '.next/server', desc: 'Server build files' },
  { path: '.next/static', desc: 'Static build files' }
];

nextDirs.forEach(dir => {
  checkDirectory(dir.path, dir.desc);
});

// Check for server.js in .next
if (fs.existsSync('.next/server')) {
  const serverFiles = fs.readdirSync('.next/server');
  log(`✅ Server build contains ${serverFiles.length} files/directories`, 'green');
} else {
  log(`❌ .next/server directory missing`, 'red');
}

// 6. Check Dependencies
log('\n📚 6. CHECKING DEPENDENCIES', 'blue');
const nodeModulesExists = checkDirectory('node_modules', 'Dependencies directory');

if (nodeModulesExists) {
  try {
    const nodeModulesSize = execSync('du -sh node_modules 2>/dev/null || echo "Unknown"', { encoding: 'utf8' }).trim();
    log(`✅ node_modules size: ${nodeModulesSize}`, 'green');
  } catch (error) {
    log(`⚠️ Could not determine node_modules size`, 'yellow');
  }
} else {
  log(`❌ Dependencies not installed. Run: npm install --only=production`, 'red');
}

// 7. Check Server.js Configuration
log('\n⚙️ 7. CHECKING SERVER.JS CONFIGURATION', 'blue');
try {
  const serverContent = fs.readFileSync('server.js', 'utf8');
  
  // Check for port configuration
  if (serverContent.includes('process.env.PORT')) {
    log(`✅ Server uses environment PORT variable`, 'green');
  } else {
    log(`⚠️ Server may not use environment PORT variable`, 'yellow');
  }
  
  // Check for hostname configuration
  if (serverContent.includes('0.0.0.0') || serverContent.includes('process.env.HOSTNAME')) {
    log(`✅ Server configured for external access`, 'green');
  } else {
    log(`⚠️ Server may only listen on localhost`, 'yellow');
  }
  
  // Check for Next.js integration
  if (serverContent.includes('next') && serverContent.includes('createServer')) {
    log(`✅ Server appears to be Next.js compatible`, 'green');
  } else {
    log(`⚠️ Server may not be properly configured for Next.js`, 'yellow');
  }
} catch (error) {
  log(`❌ Error reading server.js: ${error.message}`, 'red');
}

// 8. File Permissions Check
log('\n🔒 8. CHECKING FILE PERMISSIONS', 'blue');
try {
  const serverStats = fs.statSync('server.js');
  const serverMode = (serverStats.mode & parseInt('777', 8)).toString(8);
  log(`✅ server.js permissions: ${serverMode}`, 'green');
  
  if (serverMode.includes('7') || serverMode.includes('5')) {
    log(`✅ server.js is executable`, 'green');
  } else {
    log(`⚠️ server.js may not be executable (consider chmod 755)`, 'yellow');
  }
} catch (error) {
  log(`❌ Error checking server.js permissions: ${error.message}`, 'red');
}

// 9. Summary and Recommendations
log('\n📋 9. SUMMARY AND RECOMMENDATIONS', 'blue');

if (!allFilesExist) {
  log(`❌ CRITICAL: Missing required files. Re-extract deployment package.`, 'red');
}

if (!nodeModulesExists) {
  log(`❌ CRITICAL: Dependencies not installed. Run: npm install --only=production`, 'red');
}

log('\n🔧 NEXT STEPS:', 'blue');
log('1. Fix any critical issues shown above', 'yellow');
log('2. Check cPanel Node.js Apps for application status', 'yellow');
log('3. Review cPanel error logs for specific error messages', 'yellow');
log('4. Restart the Node.js application in cPanel', 'yellow');
log('5. Test the website: https://nomatoken.com/', 'yellow');

log('\n📞 If issues persist:', 'blue');
log('- Check cPanel Error Logs for detailed error messages', 'yellow');
log('- Contact your hosting provider support', 'yellow');
log('- Review the full troubleshooting guide: CPANEL_503_TROUBLESHOOTING_GUIDE.md', 'yellow');

log('\n✅ Diagnostic complete!', 'green');
