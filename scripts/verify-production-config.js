#!/usr/bin/env node

/**
 * Production Configuration Verification Script
 * Verifies that all required environment variables are properly set for production
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Load environment variables from .env.production
function loadProductionEnv() {
  const envPath = path.join(process.cwd(), '.env.production');
  
  if (!fs.existsSync(envPath)) {
    logError('.env.production file not found');
    return {};
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=');
      }
    }
  });
  
  return env;
}

// Check for conflicting .env.local file
function checkForConflictingFiles() {
  log('\n🔍 Checking for conflicting environment files...', 'bright');
  
  const conflictingFiles = ['.env.local'];
  let hasConflicts = false;
  
  conflictingFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logError(`Found conflicting file: ${file}`);
      logInfo(`This file will override production settings. Consider renaming to ${file}.development`);
      hasConflicts = true;
    }
  });
  
  if (!hasConflicts) {
    logSuccess('No conflicting environment files found');
  }
  
  return !hasConflicts;
}

// Verify required environment variables
function verifyRequiredVariables(env) {
  log('\n🔍 Verifying required environment variables...', 'bright');
  
  const requiredVars = [
    'NODE_ENV',
    'NEXT_PUBLIC_API_URL',
    'NEXT_PUBLIC_SITE_URL',
    'MPESA_ENVIRONMENT',
    'MPESA_CONSUMER_KEY',
    'MPESA_CONSUMER_SECRET',
    'MPESA_BUSINESS_SHORT_CODE',
    'MPESA_PASSKEY',
    'MPESA_CALLBACK_URL'
  ];
  
  let allValid = true;
  
  requiredVars.forEach(varName => {
    if (!env[varName]) {
      logError(`Missing required variable: ${varName}`);
      allValid = false;
    } else if (env[varName].includes('your') || env[varName].includes('placeholder')) {
      logWarning(`Variable ${varName} appears to contain placeholder text: ${env[varName]}`);
      allValid = false;
    } else {
      logSuccess(`${varName} is set`);
    }
  });
  
  return allValid;
}

// Verify API URL configuration
function verifyApiConfiguration(env) {
  log('\n🔍 Verifying API configuration...', 'bright');
  
  const apiUrl = env.NEXT_PUBLIC_API_URL;
  const siteUrl = env.NEXT_PUBLIC_SITE_URL;
  
  if (!apiUrl) {
    logError('NEXT_PUBLIC_API_URL is not set');
    return false;
  }
  
  if (apiUrl.includes('localhost')) {
    logError('NEXT_PUBLIC_API_URL is pointing to localhost - this will not work in production');
    logInfo(`Current value: ${apiUrl}`);
    logInfo('Should be: https://nomatoken.com/api');
    return false;
  }
  
  if (!apiUrl.startsWith('https://')) {
    logError('NEXT_PUBLIC_API_URL should use HTTPS in production');
    return false;
  }
  
  if (!apiUrl.includes('nomatoken.com')) {
    logWarning('NEXT_PUBLIC_API_URL does not point to nomatoken.com domain');
    logInfo(`Current value: ${apiUrl}`);
  }
  
  logSuccess(`API URL correctly configured: ${apiUrl}`);
  
  if (siteUrl && !siteUrl.includes('nomatoken.com')) {
    logWarning('NEXT_PUBLIC_SITE_URL does not point to nomatoken.com domain');
    logInfo(`Current value: ${siteUrl}`);
  } else if (siteUrl) {
    logSuccess(`Site URL correctly configured: ${siteUrl}`);
  }
  
  return true;
}

// Verify M-Pesa configuration
function verifyMpesaConfiguration(env) {
  log('\n🔍 Verifying M-Pesa configuration...', 'bright');
  
  const mpesaEnv = env.MPESA_ENVIRONMENT;
  const callbackUrl = env.MPESA_CALLBACK_URL;
  
  if (mpesaEnv !== 'production') {
    logWarning(`M-Pesa environment is set to: ${mpesaEnv}`);
    logInfo('For production, this should be "production"');
  } else {
    logSuccess('M-Pesa environment is set to production');
  }
  
  if (!callbackUrl) {
    logError('MPESA_CALLBACK_URL is not set');
    return false;
  }
  
  if (!callbackUrl.startsWith('https://')) {
    logError('MPESA_CALLBACK_URL must use HTTPS');
    return false;
  }
  
  if (!callbackUrl.includes('nomatoken.com')) {
    logWarning('MPESA_CALLBACK_URL does not point to nomatoken.com domain');
    logInfo(`Current value: ${callbackUrl}`);
  } else {
    logSuccess(`M-Pesa callback URL correctly configured: ${callbackUrl}`);
  }
  
  // Check for placeholder credentials
  const credentials = ['MPESA_CONSUMER_KEY', 'MPESA_CONSUMER_SECRET', 'MPESA_BUSINESS_SHORT_CODE', 'MPESA_PASSKEY'];
  let credentialsValid = true;
  
  credentials.forEach(cred => {
    if (env[cred] && (env[cred].includes('YOUR_') || env[cred].includes('your_'))) {
      logError(`${cred} appears to contain placeholder text`);
      credentialsValid = false;
    } else if (env[cred]) {
      logSuccess(`${cred} is configured`);
    }
  });
  
  return credentialsValid;
}

// Main verification function
function main() {
  log('🚀 NomaToken Production Configuration Verification', 'bright');
  log('================================================', 'bright');
  
  const env = loadProductionEnv();
  
  if (Object.keys(env).length === 0) {
    logError('Failed to load .env.production file');
    process.exit(1);
  }
  
  logSuccess('Loaded .env.production file');
  
  const checks = [
    checkForConflictingFiles(),
    verifyRequiredVariables(env),
    verifyApiConfiguration(env),
    verifyMpesaConfiguration(env)
  ];
  
  const allPassed = checks.every(check => check);
  
  log('\n📋 Summary', 'bright');
  log('==========', 'bright');
  
  if (allPassed) {
    logSuccess('All configuration checks passed! ✨');
    logInfo('Your production environment should be ready for M-Pesa payments.');
  } else {
    logError('Some configuration issues were found.');
    logInfo('Please fix the issues above before deploying to production.');
    process.exit(1);
  }
}

// Run the verification
main();
