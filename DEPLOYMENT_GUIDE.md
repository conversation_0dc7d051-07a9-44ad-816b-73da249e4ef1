# NomaToken Production Deployment Guide

## 🚨 M-Pesa Payment Fix - Critical Update

This guide addresses the M-Pesa payment integration error where API calls were pointing to `localhost:3000` instead of the production server.

### ❌ Problem Identified

1. **Root Cause**: `NEXT_PUBLIC_API_URL` was set to placeholder value `https://yourdomain.com/api`
2. **Environment Conflict**: `.env.local` file was overriding production settings
3. **Result**: <PERSON><PERSON> was calling `http://localhost:3000/api/mpesa/payment/initiate` instead of production API

### ✅ Solution Applied

1. **Fixed API URL**: Updated `NEXT_PUBLIC_API_URL` to `https://nomatoken.com/api`
2. **Resolved Conflicts**: Renamed `.env.local` to `.env.local.development`
3. **Added Missing Variables**: Added `NEXT_PUBLIC_SITE_URL=https://nomatoken.com`
4. **Updated Placeholders**: Fixed email addresses and other placeholder values

## 📋 Deployment Steps

### Step 1: Verify Configuration

Run the verification script to ensure all settings are correct:

```bash
node scripts/verify-production-config.js
```

You should see all green checkmarks ✅.

### Step 2: Build Production Package

```bash
# Install dependencies
npm install

# Build the production application
npm run build

# Create production package (if you have the build script)
./build-production.sh
```

### Step 3: Upload to cPanel

1. **Backup Current Site**: Download your current site files as backup
2. **Upload Files**: Upload the built application to your cPanel file manager
3. **Extract**: Extract files to your domain's public_html directory
4. **Set Environment**: Ensure `.env.production` is in the root directory

### Step 4: Configure cPanel Node.js App

1. **Go to Node.js Apps** in cPanel
2. **Set Configuration**:
   - **Node.js Version**: 18.x or higher
   - **Application Mode**: Production
   - **Startup File**: `server.js`
   - **Application Root**: Your domain directory
3. **Install Dependencies**: Click "Run NPM Install"
4. **Restart Application**

### Step 5: Environment Variables in cPanel

If your cPanel supports environment variables through the interface:

1. Go to **Node.js Apps** → **Environment Variables**
2. Add these critical variables:
   ```
   NODE_ENV=production
   NEXT_PUBLIC_API_URL=https://nomatoken.com/api
   NEXT_PUBLIC_SITE_URL=https://nomatoken.com
   MPESA_ENVIRONMENT=production
   ```

**Note**: Most cPanel setups will read from `.env.production` automatically.

## 🧪 Testing the Fix

### Test 1: Check API Endpoint

1. Open browser developer tools (F12)
2. Go to your site: https://nomatoken.com
3. Try to initiate an M-Pesa payment
4. Check the Network tab - API calls should now go to `https://nomatoken.com/api/mpesa/payment/initiate`

### Test 2: M-Pesa Payment Flow

1. Navigate to the token purchase page
2. Select M-Pesa payment method
3. Enter a valid Kenyan phone number (format: 254XXXXXXXXX)
4. Enter an amount between $10-$10,000
5. Click "Pay with M-Pesa"
6. Verify you receive the STK push on your phone

### Test 3: Check Console Errors

1. Open browser console (F12 → Console)
2. Look for any remaining `localhost` references
3. Verify no CORS or network errors

## 🔧 Troubleshooting

### Issue: Still seeing localhost errors

**Solution**: 
- Clear browser cache completely
- Check if `.env.local` file still exists (should be renamed)
- Verify cPanel app restarted after changes

### Issue: M-Pesa credentials error

**Solution**:
- Verify M-Pesa production credentials in `.env.production`
- Ensure `MPESA_ENVIRONMENT=production`
- Check Safaricom developer portal for credential status

### Issue: CORS errors

**Solution**:
- Verify `NEXT_PUBLIC_SITE_URL` matches your domain exactly
- Check `ALLOWED_ORIGINS` in server configuration
- Ensure HTTPS is properly configured

### Issue: 404 on API routes

**Solution**:
- Verify Next.js app is running in production mode
- Check cPanel Node.js app configuration
- Ensure `server.js` is set as startup file

## 📁 File Structure After Fix

```
nomatoken/
├── .env.production              # ✅ Updated with correct URLs
├── .env.local.development       # ✅ Renamed to avoid conflicts
├── scripts/
│   └── verify-production-config.js  # ✅ New verification script
├── server.js                    # ✅ Production server
└── ... (other files)
```

## 🔐 Security Notes

1. **Environment Files**: Never commit `.env.production` to version control
2. **M-Pesa Credentials**: Keep production credentials secure
3. **HTTPS**: Ensure all production URLs use HTTPS
4. **Callback URLs**: Verify callback URLs are accessible from Safaricom

## 📞 Support

If you encounter issues after deployment:

1. Check cPanel error logs
2. Run the verification script locally
3. Test API endpoints directly using curl or Postman
4. Verify M-Pesa credentials with Safaricom

## ✅ Success Indicators

After successful deployment, you should see:

- ✅ M-Pesa payment requests go to `https://nomatoken.com/api/mpesa/payment/initiate`
- ✅ No `localhost` references in browser network tab
- ✅ STK push notifications received on test phone
- ✅ No CORS or network errors in browser console
- ✅ Payment status polling works correctly

---

**Last Updated**: $(date)
**Status**: Ready for Production Deployment
