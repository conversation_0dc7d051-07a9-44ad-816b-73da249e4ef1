# Reown AppKit Configuration
NEXT_PUBLIC_REOWN_PROJECT_ID=1eeba23b8a565e4340dab5918b836327

# BSC Network Configuration
NEXT_PUBLIC_BSC_RPC_URL=https://bsc-dataseed1.binance.org/
NEXT_PUBLIC_BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

# NOMA Token Contract (Replace with actual contract address when deployed)
NEXT_PUBLIC_NOMA_TOKEN_CONTRACT=0x0000000000000000000000000000000000000000

# Token Sale Contract (Replace with actual contract address when deployed)
NEXT_PUBLIC_TOKEN_SALE_CONTRACT=0x0000000000000000000000000000000000000000

# Supported Tokens for Purchase
NEXT_PUBLIC_USDT_CONTRACT=0x55d398326f99059fF775485246999027B3197955
NEXT_PUBLIC_BUSD_CONTRACT=0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56

# Network Configuration
NEXT_PUBLIC_DEFAULT_CHAIN_ID=56
NEXT_PUBLIC_ENABLE_TESTNET=false

# M-Pesa Configuration (Sandbox)
MPESA_ENVIRONMENT=sandbox
MPESA_CONSUMER_KEY=8iMnBuw3nU99W4nOWEgIWevqqGN41EpzqIWtkREFc1JRdOva
MPESA_CONSUMER_SECRET=n5buh6et3dqByPGFBDdYnNfbZ8ZG01rMlYl8h3OhFl48BjYjhJtboWPmAAAAoktF
MPESA_BUSINESS_SHORT_CODE=174379
MPESA_PASSKEY=bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919
MPESA_CALLBACK_URL=https://learning-active-heron.ngrok-free.app/api/mpesa/payment/callback

# M-Pesa Security & Rate Limiting (Development)
MPESA_MAX_REQUESTS_PER_MINUTE=60
MPESA_MAX_PAYMENT_ATTEMPTS=10
MPESA_PAYMENT_TIMEOUT_MS=120000
MPESA_STATUS_CHECK_INTERVAL_MS=3000

# Token Configuration
NOMA_TOKEN_PRICE=0.0245
MIN_PURCHASE_AMOUNT=1
MAX_PURCHASE_AMOUNT=1000

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Environment
NODE_ENV=development
