@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  
  /* Custom Noma Token Design Tokens */
  --noma-primary: 16 185 129;
  --noma-secondary: 59 130 246;
  --noma-accent: 245 158 11;
  --noma-success: 34 197 94;
  --noma-warning: 251 146 60;
  --noma-error: 239 68 68;
  --noma-neutral: 107 114 128;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: var(--noma-primary);
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: var(--noma-accent);
    --accent-foreground: 0 0% 9%;
    --destructive: var(--noma-error);
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: var(--noma-primary);
    --chart-1: var(--noma-primary);
    --chart-2: var(--noma-secondary);
    --chart-3: var(--noma-accent);
    --chart-4: var(--noma-success);
    --chart-5: var(--noma-warning);
    --radius: 0.75rem;
  }
  
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: var(--noma-primary);
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: var(--noma-accent);
    --accent-foreground: 0 0% 98%;
    --destructive: var(--noma-error);
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: var(--noma-primary);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }
}

@layer components {
  .gradient-primary {
    @apply bg-gradient-to-r from-emerald-500 via-emerald-600 to-emerald-700;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700;
  }
  
  .gradient-accent {
    @apply bg-gradient-to-r from-amber-500 via-amber-600 to-amber-700;
  }
  
  .glass-effect {
    @apply backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10;
  }
  
  .neon-glow {
    @apply shadow-lg shadow-emerald-500/25 dark:shadow-emerald-400/25;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary/60 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/80;
}

/* Font families */
.font-inter {
  font-family: var(--font-inter), sans-serif;
}

.font-poppins {
  font-family: var(--font-poppins), sans-serif;
}

/* Coin Animation Styles */
@keyframes coinFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes coinFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes coinGlow {
  0%, 100% {
    filter: drop-shadow(0 0 5px rgba(16, 185, 129, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(16, 185, 129, 0.6));
  }
}

@keyframes coinSpin {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

@keyframes coinBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes coinParticle {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

.coin-fall {
  animation: coinFall linear infinite;
}

.coin-float {
  animation: coinFloat 3s ease-in-out infinite;
}

.coin-glow {
  animation: coinGlow 2s ease-in-out infinite;
}

.coin-spin {
  animation: coinSpin 2s linear infinite;
}

.coin-bounce {
  animation: coinBounce 1s ease-in-out;
}

.coin-particle {
  animation: coinParticle 1s ease-out forwards;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .coin-fall,
  .coin-float,
  .coin-glow,
  .coin-spin,
  .coin-bounce,
  .coin-particle {
    animation: none;
    transition: none;
  }

  .coin-static {
    opacity: 0.05;
    transform: none;
    position: static;
  }

  /* Disable all motion-based animations when reduced motion is preferred */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Keep essential animations but make them instant */
  .fade-in,
  .slide-in,
  .scale-in {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Accessibility: Focus indicators for coin animations */
.coin-animation-container:focus-within {
  outline: 2px solid rgb(var(--noma-primary));
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .coin-fall,
  .coin-float,
  .coin-glow,
  .coin-particle {
    filter: contrast(1.5) brightness(1.2);
  }
}